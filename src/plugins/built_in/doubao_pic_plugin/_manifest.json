{"manifest_version": 1, "name": "豆包图片生成插件 (Doubao Image Generator)", "version": "2.0.0", "description": "基于火山引擎豆包模型的AI图片生成插件，支持智能LLM判定、高质量图片生成、结果缓存和多尺寸支持。", "author": {"name": "MaiBot团队", "url": "https://github.com/MaiM-with-u"}, "license": "GPL-v3.0-or-later", "host_application": {"min_version": "0.8.0", "max_version": "0.8.0"}, "homepage_url": "https://github.com/MaiM-with-u/maibot", "repository_url": "https://github.com/MaiM-with-u/maibot", "keywords": ["ai", "image", "generation", "do<PERSON>o", "volcengine", "art"], "categories": ["AI Tools", "Image Processing", "Content Generation"], "default_locale": "zh-CN", "locales_path": "_locales", "plugin_info": {"is_built_in": true, "plugin_type": "content_generator", "api_dependencies": ["volcengine"], "components": [{"type": "action", "name": "doubao_image_generation", "description": "根据描述使用火山引擎豆包API生成高质量图片", "activation_modes": ["llm_judge", "keyword"], "keywords": ["画", "图片", "生成", "画画", "绘制"]}], "features": ["智能LLM判定生成时机", "高质量AI图片生成", "结果缓存机制", "多种图片尺寸支持", "完整的错误处理"]}}