{"manifest_version": 1, "name": "Hello World 示例插件 (Hello World Plugin)", "version": "1.0.0", "description": "我的第一个MaiCore插件，包含问候功能和时间查询等基础示例", "author": {"name": "MaiBot开发团队", "url": "https://github.com/MaiM-with-u"}, "license": "GPL-v3.0-or-later", "host_application": {"min_version": "0.8.0", "max_version": "0.8.0"}, "homepage_url": "https://github.com/MaiM-with-u/maibot", "repository_url": "https://github.com/MaiM-with-u/maibot", "keywords": ["demo", "example", "hello", "greeting", "tutorial"], "categories": ["Examples", "Tutorial"], "default_locale": "zh-CN", "locales_path": "_locales", "plugin_info": {"is_built_in": false, "plugin_type": "example", "components": [{"type": "action", "name": "hello_greeting", "description": "向用户发送问候消息"}, {"type": "action", "name": "bye_greeting", "description": "向用户发送告别消息", "activation_modes": ["keyword"], "keywords": ["再见", "bye", "88", "拜拜"]}, {"type": "command", "name": "time", "description": "查询当前时间", "pattern": "/time"}], "features": ["问候和告别功能", "时间查询命令", "配置文件示例", "新手教程代码"]}}